# California Driving School Website

A modern, responsive single-page website for California Driving School located in Nairobi, Kenya. This website showcases world-class driving education with California standards adapted for the Kenyan market.

## 🌟 Features

### Unique Design Elements
- **California-themed color scheme** with orange and gold gradients
- **Modern, clean layout** with smooth animations
- **Mobile-first responsive design** that works on all devices
- **Interactive elements** including animated counters and testimonial slider
- **WhatsApp integration** for instant communication
- **Smooth scrolling navigation** with fixed header

### Sections
1. **Hero Section** - Eye-catching introduction with animated statistics
2. **About Section** - Six key features highlighting California standards
3. **Programs Section** - Three comprehensive training packages
4. **Features Section** - Unique offerings like VR simulation and mobile app
5. **Testimonials** - Auto-rotating customer reviews
6. **Contact Section** - Contact form and business information
7. **Footer** - Complete site navigation and social links

### Technical Features
- **Pure HTML, CSS, and JavaScript** - No frameworks required
- **CSS Grid and Flexbox** for modern layouts
- **CSS Custom Properties** for consistent theming
- **Intersection Observer API** for scroll animations
- **Form validation** with user-friendly notifications
- **Optimized performance** with lazy loading support

## 🎨 Design Highlights

### California Theme
- **Primary Color**: #FF6B35 (California Orange)
- **Secondary Color**: #FFD23F (California Gold)
- **Accent Color**: #1E3A8A (Deep Blue)
- **Typography**: Poppins font family for modern readability

### Unique Features vs Reference Site
1. **Enhanced Visual Design**: California-themed gradients and modern card layouts
2. **Interactive Elements**: Animated counters, smooth transitions, and hover effects
3. **Better Mobile Experience**: Improved responsive design with hamburger menu
4. **Local Context**: Nairobi-specific content while maintaining California branding
5. **Advanced Features**: VR simulation training, mobile app support, multilingual instruction
6. **Professional Contact Form**: With validation and success notifications

## 📱 Responsive Design

The website is fully responsive and optimized for:
- **Desktop** (1200px+)
- **Tablet** (768px - 1024px)
- **Mobile** (320px - 767px)

## 🚀 Getting Started

1. **Open the website**: Simply open `index.html` in any modern web browser
2. **Local Development**: Use a local server for best experience:
   ```bash
   # Using Python
   python -m http.server 8000
   
   # Using Node.js
   npx serve .
   ```

## 📁 File Structure

```
california-websites/
├── index.html              # Main HTML file
├── assets/
│   ├── css/
│   │   └── style.css       # Main stylesheet
│   ├── js/
│   │   └── script.js       # Interactive functionality
│   └── images/
│       └── california-logo.png  # School logo
└── README.md               # This file
```

## 🎯 Key Differentiators from Reference Site

1. **Modern Design Language**: Clean, contemporary design with California aesthetics
2. **Enhanced User Experience**: Smooth animations, better navigation, and interactive elements
3. **Professional Branding**: Consistent California theme throughout the site
4. **Advanced Features**: VR training, mobile app integration, multilingual support
5. **Better Content Organization**: Clear program structure with detailed features
6. **Improved Contact Experience**: Professional form with validation and multiple contact methods

## 📞 Contact Information

- **Location**: Westlands, Nairobi, Kenya
- **Phone**: +254 712 345 678 / +254 798 765 432
- **Email**: <EMAIL>
- **Hours**: Monday-Saturday: 7:00 AM - 8:00 PM, Sunday: 9:00 AM - 5:00 PM

## 🛠️ Customization

The website is built with maintainability in mind:

- **CSS Variables**: Easy color and spacing customization
- **Modular JavaScript**: Each feature is in its own function
- **Semantic HTML**: Clean, accessible markup
- **Responsive Images**: Logo can be easily replaced

## 📈 Performance

- **Lightweight**: Minimal dependencies, fast loading
- **Optimized**: Efficient CSS and JavaScript
- **Accessible**: Semantic HTML and ARIA labels
- **SEO-Friendly**: Proper meta tags and structure

---

**Designed with ❤️ in Nairobi for California Driving School**
